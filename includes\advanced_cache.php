<?php
/**
 * Advanced Cache System for Prime HRM
 * Maximum performance without affecting existing functionality
 */

class PrimeAdvancedCache {
    private static $instance = null;
    private $cacheDir = '';
    private $enabled = true;
    private $defaultTTL = 3600; // 1 hour
    
    public function __construct() {
        $this->cacheDir = dirname(__DIR__) . '/cache/';
        $this->ensureCacheDirectory();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function ensureCacheDirectory() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create .htaccess to protect cache directory
        $htaccessFile = $this->cacheDir . '.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    public function set($key, $data, $ttl = null) {
        if (!$this->enabled) return false;
        
        $ttl = $ttl ?: $this->defaultTTL;
        $cacheData = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        $filename = $this->getCacheFilename($key);
        return file_put_contents($filename, serialize($cacheData)) !== false;
    }
    
    public function get($key) {
        if (!$this->enabled) return null;
        
        $filename = $this->getCacheFilename($key);
        if (!file_exists($filename)) return null;
        
        $cacheData = unserialize(file_get_contents($filename));
        
        if (time() > $cacheData['expires']) {
            unlink($filename);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    public function delete($key) {
        $filename = $this->getCacheFilename($key);
        if (file_exists($filename)) {
            return unlink($filename);
        }
        return true;
    }
    
    public function clear() {
        $files = glob($this->cacheDir . 'cache_*.dat');
        foreach ($files as $file) {
            unlink($file);
        }
        return true;
    }
    
    private function getCacheFilename($key) {
        return $this->cacheDir . 'cache_' . md5($key) . '.dat';
    }
    
    public function getStats() {
        $files = glob($this->cacheDir . 'cache_*.dat');
        $totalSize = 0;
        $validItems = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $data = unserialize(file_get_contents($file));
            if (time() <= $data['expires']) {
                $validItems++;
            }
        }
        
        return [
            'total_items' => count($files),
            'valid_items' => $validItems,
            'total_size' => $totalSize,
            'cache_dir' => $this->cacheDir
        ];
    }
}

function outputAdvancedCache() {
    echo '
    <!-- Advanced Cache System for Maximum Performance -->
    <style>
        .cache-indicator {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .cache-indicator.show {
            opacity: 1;
            transform: translateY(-2px);
        }
        
        .cache-indicator.performance {
            background: linear-gradient(45deg, #007bff, #6610f2);
            box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
        }
    </style>
    
    <div id="cacheIndicator" class="cache-indicator">⚡ Cache Active</div>
    
    <script>
        // Advanced Cache System - Maximum Performance
        (function() {
            "use strict";
            
            // Performance-focused cache object
            window.primeAdvancedCache = {
                version: "2.0.0",
                enabled: true,
                stats: {
                    hits: 0,
                    misses: 0,
                    sets: 0
                },
                
                // Enhanced localStorage with compression
                set: function(key, value, hours) {
                    hours = hours || 24;
                    const expires = new Date();
                    expires.setTime(expires.getTime() + (hours * 60 * 60 * 1000));
                    
                    const cacheData = {
                        value: this.compress(value),
                        expires: expires.getTime(),
                        created: new Date().getTime(),
                        compressed: true
                    };
                    
                    try {
                        localStorage.setItem("prime_adv_" + key, JSON.stringify(cacheData));
                        this.stats.sets++;
                        return true;
                    } catch (e) {
                        this.cleanup();
                        try {
                            localStorage.setItem("prime_adv_" + key, JSON.stringify(cacheData));
                            this.stats.sets++;
                            return true;
                        } catch (e2) {
                            console.warn("Cache storage failed:", e2);
                            return false;
                        }
                    }
                },
                
                get: function(key) {
                    try {
                        const cached = localStorage.getItem("prime_adv_" + key);
                        if (!cached) {
                            this.stats.misses++;
                            return null;
                        }
                        
                        const data = JSON.parse(cached);
                        
                        if (new Date().getTime() > data.expires) {
                            localStorage.removeItem("prime_adv_" + key);
                            this.stats.misses++;
                            return null;
                        }
                        
                        this.stats.hits++;
                        return data.compressed ? this.decompress(data.value) : data.value;
                    } catch (e) {
                        console.warn("Cache retrieval failed:", e);
                        this.stats.misses++;
                        return null;
                    }
                },
                
                // Simple compression for text data
                compress: function(data) {
                    if (typeof data === "string" && data.length > 100) {
                        try {
                            return btoa(encodeURIComponent(data));
                        } catch (e) {
                            return data;
                        }
                    }
                    return data;
                },
                
                decompress: function(data) {
                    if (typeof data === "string") {
                        try {
                            return decodeURIComponent(atob(data));
                        } catch (e) {
                            return data;
                        }
                    }
                    return data;
                },
                
                // Intelligent cleanup
                cleanup: function() {
                    const keys = Object.keys(localStorage);
                    const cacheKeys = keys.filter(k => k.startsWith("prime_adv_"));
                    
                    // Remove expired items first
                    let cleaned = 0;
                    cacheKeys.forEach(function(key) {
                        try {
                            const data = JSON.parse(localStorage.getItem(key));
                            if (new Date().getTime() > data.expires) {
                                localStorage.removeItem(key);
                                cleaned++;
                            }
                        } catch (e) {
                            localStorage.removeItem(key);
                            cleaned++;
                        }
                    });
                    
                    // If still too full, remove oldest items
                    if (cleaned < 5) {
                        const remaining = Object.keys(localStorage).filter(k => k.startsWith("prime_adv_"));
                        remaining.sort().slice(0, 10).forEach(function(key) {
                            localStorage.removeItem(key);
                        });
                    }
                },
                
                clear: function(pattern) {
                    const keys = Object.keys(localStorage);
                    const targetKeys = pattern ? 
                        keys.filter(k => k.startsWith("prime_adv_") && k.includes(pattern)) :
                        keys.filter(k => k.startsWith("prime_adv_"));
                    
                    targetKeys.forEach(function(key) {
                        localStorage.removeItem(key);
                    });
                    
                    this.stats = { hits: 0, misses: 0, sets: 0 };
                },
                
                getStats: function() {
                    const keys = Object.keys(localStorage);
                    const cacheKeys = keys.filter(k => k.startsWith("prime_adv_"));
                    
                    return {
                        ...this.stats,
                        items: cacheKeys.length,
                        hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) * 100 || 0,
                        size: JSON.stringify(localStorage).length
                    };
                }
            };
            
            // Performance monitoring and optimization
            const performanceMonitor = {
                startTime: performance.now(),
                
                init: function() {
                    this.cachePageData();
                    this.optimizeImages();
                    this.preloadCriticalResources();
                    this.setupIntersectionObserver();
                },
                
                cachePageData: function() {
                    const pageData = {
                        url: window.location.href,
                        title: document.title,
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent.substring(0, 100)
                    };
                    
                    primeAdvancedCache.set("page_" + window.location.pathname, pageData, 2);
                },
                
                optimizeImages: function() {
                    const images = document.querySelectorAll("img[src]");
                    images.forEach(function(img) {
                        if (!img.loading) {
                            img.loading = "lazy";
                        }
                    });
                },
                
                preloadCriticalResources: function() {
                    const criticalLinks = document.querySelectorAll("link[rel=stylesheet]");
                    criticalLinks.forEach(function(link) {
                        if (!link.hasAttribute("data-preloaded")) {
                            link.setAttribute("data-preloaded", "true");
                        }
                    });
                },
                
                setupIntersectionObserver: function() {
                    if ("IntersectionObserver" in window) {
                        const observer = new IntersectionObserver(function(entries) {
                            entries.forEach(function(entry) {
                                if (entry.isIntersecting) {
                                    const element = entry.target;
                                    if (element.dataset.src) {
                                        element.src = element.dataset.src;
                                        element.removeAttribute("data-src");
                                        observer.unobserve(element);
                                    }
                                }
                            });
                        });
                        
                        document.querySelectorAll("[data-src]").forEach(function(el) {
                            observer.observe(el);
                        });
                    }
                }
            };
            
            // Initialize when DOM is ready
            document.addEventListener("DOMContentLoaded", function() {
                performanceMonitor.init();
                
                // Show performance indicator
                const indicator = document.getElementById("cacheIndicator");
                if (indicator) {
                    indicator.classList.add("show");
                    
                    // Show performance stats after load
                    setTimeout(function() {
                        const loadTime = performance.now() - performanceMonitor.startTime;
                        if (loadTime < 1000) {
                            indicator.classList.add("performance");
                            indicator.textContent = "⚡ Fast Load (" + Math.round(loadTime) + "ms)";
                        }
                        
                        setTimeout(function() {
                            indicator.classList.remove("show");
                        }, 3000);
                    }, 500);
                }
                
                // Cache performance data
                if (window.performance && window.performance.timing) {
                    const timing = window.performance.timing;
                    const loadTime = timing.loadEventEnd - timing.navigationStart;
                    
                    primeAdvancedCache.set("perf_" + Date.now(), {
                        page: window.location.pathname,
                        loadTime: loadTime,
                        timestamp: Date.now()
                    }, 1);
                }
            });
            
            // Global functions
            window.clearAdvancedCache = function() {
                primeAdvancedCache.clear();
                console.log("Advanced cache cleared");
            };
            
            window.showAdvancedCacheStats = function() {
                const stats = primeAdvancedCache.getStats();
                console.log("Advanced Cache Stats:", stats);
                return stats;
            };
            
            // Additional performance optimizations
            const additionalOptimizations = {
                // Prefetch likely navigation targets
                prefetchNavigation: function() {
                    const navLinks = document.querySelectorAll("nav a[href], .navbar a[href]");
                    navLinks.forEach(function(link) {
                        if (link.hostname === window.location.hostname) {
                            const prefetchLink = document.createElement("link");
                            prefetchLink.rel = "prefetch";
                            prefetchLink.href = link.href;
                            document.head.appendChild(prefetchLink);
                        }
                    });
                },

                // Optimize form submissions
                optimizeForms: function() {
                    const forms = document.querySelectorAll("form");
                    forms.forEach(function(form) {
                        form.addEventListener("submit", function() {
                            // Cache form data for recovery
                            const formData = new FormData(form);
                            const data = {};
                            for (let [key, value] of formData.entries()) {
                                data[key] = value;
                            }
                            primeAdvancedCache.set("form_backup_" + form.id || "default", data, 0.5);
                        });
                    });
                },

                // Optimize table rendering
                optimizeTables: function() {
                    const tables = document.querySelectorAll("table");
                    tables.forEach(function(table) {
                        if (table.rows.length > 50) {
                            table.style.tableLayout = "fixed";
                        }
                    });
                },

                // Memory management
                manageMemory: function() {
                    // Clear old cache entries when memory is low
                    if (navigator.deviceMemory && navigator.deviceMemory < 4) {
                        primeAdvancedCache.cleanup();
                    }
                }
            };

            // Apply additional optimizations
            setTimeout(function() {
                additionalOptimizations.prefetchNavigation();
                additionalOptimizations.optimizeForms();
                additionalOptimizations.optimizeTables();
                additionalOptimizations.manageMemory();
            }, 2000);

            // Cleanup old performance data periodically
            setTimeout(function() {
                primeAdvancedCache.clear("perf_");
            }, 10000);

        })();
    </script>
    ';
}

function includeAdvancedCache() {
    outputAdvancedCache();
}
?>
