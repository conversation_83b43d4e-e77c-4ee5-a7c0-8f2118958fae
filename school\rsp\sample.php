<?php
session_start();
require_once 'db/dbconfig.php';
require_once 'db/ipcrfController.php';

if (isset($_SESSION['empnum'])) {
    $empnum = $_SESSION['empnum'];
} else {
    // Not logged in or empnum not set
    die("Unauthorized access.");
}



// Debug session
error_log("Session ID: " . (isset($_SESSION['id']) ? $_SESSION['id'] : 'Not set'));
error_log("Session shortname: " . (isset($_SESSION['shortname']) ? $_SESSION['shortname'] : 'Not set'));

// Get selected school year
$selected_sy = $_GET['selected_sy'] ?? '2025-2026';
$rating_period = 'SY ' . $selected_sy;

// Initialize IPCRF Controller
$ipcrfController = null;
try {
    if (isset($pdo)) {
        $ipcrfController = new IPCRFController($pdo);
        error_log("IPCRF Controller initialized successfully");
    } else {
        error_log("PDO connection not available");
    }
} catch (Exception $e) {
    error_log("Error initializing IPCRF Controller: " . $e->getMessage());
}

// Get header information
$header_info = array(
    'ratee_name' => strtoupper($_SESSION['shortname'] ?? ''),
    'current_date' => date('F d, Y'),
    'rating_period' => $rating_period
);

if ($ipcrfController) {
    try {
        $db_header_info = $ipcrfController->getHeaderInformation();
        if (!empty($db_header_info)) {
            $header_info = array_merge($header_info, $db_header_info);
        }
    } catch (Exception $e) {
        error_log("Error fetching header info: " . $e->getMessage());
    }
}

// Get IPCRF data
$ipcrf_data = array();
if ($ipcrfController) {
    try {
        $ipcrf_data = $ipcrfController->getIPCRFCatchupData($rating_period);
        // Debug: Check if data is retrieved
        error_log("IPCRF Data Count: " . count($ipcrf_data));
        error_log("Rating Period: " . $rating_period);
    } catch (Exception $e) {
        error_log("Error fetching IPCRF data: " . $e->getMessage());
    }
} else {
    error_log("IPCRF Controller not initialized");
}
?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPCRF Catch-up Plan - Print</title>
    <style>
    @page {
        size: A4 landscape;
        margin: 0.5in;
    }

    body {
        font-family: Arial, sans-serif;
        font-size: 12px;
        line-height: 1.2;
        margin: 0;
        padding: 0;
        background: white;
    }

    .header {
        text-align: center;
        margin-bottom: 20px;
    }

    .logo {
        width: 60px;
        height: 60px;
        margin: 0 auto 10px;
    }

    .header h1 {
        font-size: 14px;
        font-weight: bold;
        margin: 5px 0;
        text-transform: uppercase;
    }

    .header h2 {
        font-size: 12px;
        font-weight: bold;
        margin: 3px 0;
    }

    .header p {
        font-size: 10px;
        margin: 2px 0;
    }

    .form-title {
        text-align: center;
        font-weight: bold;
        font-size: 12px;
        margin: 20px 0;
        text-decoration: underline;
    }

    .commitment-text {
        text-align: justify;
        margin: 15px 0;
        text-indent: 50px;
        line-height: 1.4;
    }

    .info-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }

    .info-table td {
        border: 1px solid #000;
        padding: 5px;
        font-size: 10px;
    }

    .info-table .label {
        font-weight: bold;
        width: 15%;
    }

    .main-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }

    .main-table th,
    .main-table td {
        border: 2px solid #000;
        padding: 8px;
        text-align: center;
        vertical-align: middle;
        font-size: 10px;
    }

    .main-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .main-table .kra-header {
        width: 12%;
    }

    .main-table .objectives-header {
        width: 18%;
    }

    .main-table .target-header {
        width: 18%;
    }

    .main-table .development-header {
        width: 18%;
    }

    .main-table .strategies-header {
        width: 17%;
    }

    .main-table .actions-header {
        width: 17%;
    }

    .signature-section {
        margin-top: 30px;
    }

    .signature-table {
        width: 100%;
        border-collapse: collapse;
    }

    .signature-table td {
        border: none;
        padding: 10px;
        text-align: center;
        vertical-align: top;
        margin: 0px;
        /* Change to top for closer stacking */

    }

    .signature-line {
        border-top: 1px solid #000;
        /* Solid black line */
        width: 250px;
        /* Adjust width as needed */
        margin-bottom: 0px;
        /* Space between line and name */
    }

    .signature-line::after {
        content: "";
        display: block;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        margin-top: 2px;
    }


    .signature-label {
        font-weight: bold;
        font-size: 10px;
    }

    .signature-image {
        height: 20px !important;
        /* Reduced height */
        margin: -10px auto 0 auto;
        /* Negative top margin pulls image up */
        padding: 0;
        position: relative;
        z-index: -1;
        /* Ensure signature appears above line */

    }


    .signature-image img {
        height: 80px;
        /* Smaller image for tighter layout */
        margin-bottom: 0px;
    }

    .signature-title {
        position: relative;
        z-index: 3;
        font-weight: bold;
        margin-top: -25px;
        /* Pulls name closer to signature */
        font-size: 1.1rem;
    }

    .signature-position {
        font-size: 10px;
        font-style: italic;
        margin-top: -20px;
        line-height: 1.1;
    }

    @media print {
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        .container {
            margin: auto;
            max-width: 95%;
        }

        .no-print {
            display: none;
        }

        /* body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        .no-print {
            display: none;
        } */
    }
    </style>
</head>

<body>
    <!-- Print Button -->
    <div class="no-print" style="text-align: center; margin: 10px 0;">
        <button onclick="window.print()"
            style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            <i class="fas fa-print"></i> Print Document
        </button>
        <button onclick="window.close()"
            style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>
    <!--Print Selection-->
    <div class="no-print" style="text-align: center; margin-bottom: 10px;">
        <label for="paperSize">Paper Size:</label>
        <select id="paperSize">
            <option value="A3">A3</option>
            <option value="A4">A4</option>
            <option value="A5">A5</option>
            <option value="8.5in 13in">Long (8.5 x 13 in)</option>
            <option value="Letter">Letter</option>
            <option value="Legal">Legal</option>
            <option value="B5">B5</option>
            <option value="Tabloid">Tabloid</option>

        </select>

        <label for="orientation">Orientation:</label>
        <select id="orientation">
            <option value="portrait">Portrait</option>
            <option value="landscape">Landscape</option>
        </select>

        <button onclick="applyPrintSettings()">Apply Settings</button>
    </div>


    <!-- Header -->
    <div class="header">
        <div class="logo">
            <img src="/prime-hrm/images/DepEd.png" alt="DepEd Logo" style="width: 60px; height: 60px;"
                onerror="this.style.display='none'">
        </div>
        <h1>Republic of the Philippines</h1>
        <h1>Department of Education</h1>
        <h2>REGION VII - CENTRAL VISAYAS</h2>
        <p>SCHOOLS DIVISION OF BAIS CITY</p>
    </div>

    <!-- Form Title -->
    <div class="form-title">
        INDIVIDUAL PERFORMANCE COMMITMENT AND REVIEW FORM (IPCRF) - CATCH-UP PLAN
    </div>

    <!-- Commitment Text -->
    <div class="commitment-text">
        I, <u><strong><?php echo $header_info['ratee_name']; ?></strong></u>, hereby commit to deliver and agree to be
        rated on the attainment of the following targets in accordance with the indicated measures for the period
        <strong><?php echo $header_info['rating_period']; ?></strong>.
    </div>

    <!-- Information Table -->
    <table class="info-table">
        <tr>
            <td class="label"><strong>NAME OF RATEE:</strong></td>
            <td style="width: 35%;"><?php echo strtoupper($_SESSION['shortname'] ?? ''); ?></td>
            <td class="label"><strong>NAME OF RATER:</strong></td>
            <?php
                            $sh_qry = "SELECT * FROM tbl_users WHERE station = '" . ($_SESSION['station'] ?? '') . "' AND usertype = ?";
                            $sh_stmt = $connection->prepare($sh_qry);
                            $sh_stmt->bind_param("s", $usertype);
                            $usertype = '1';
                            $sh_stmt->execute();
                            $sh_res = $sh_stmt->get_result();
                            $sh_row = $sh_res->fetch_assoc();
                            if ($sh_row && isset($sh_row['fname'])) {
                                $sh_name = $sh_row['fname'].' '.substr((($sh_row['mname']=="") ?"":($sh_row['mname'])), 0, 1).'.'.' '.$sh_row['lname'].''.(($sh_row['extname']=="") ?"":" ".($sh_row['extname']));
                            } else {
                                $sh_name = 'School Head';
                            }
                            ?>
            <td style="width: 35%;"><?php echo strtoupper($sh_name);?></td>
        </tr>
        <tr>
            <td class="label"><strong>POSITION:</strong></td>
            <td><?php echo strtoupper($_SESSION['position'] ?? ''); ?></td>
            <td class="labe" style="font-weight: bold; width: 15%;"><strong>POSITION:</strong></td>
            <?php
                                $sh_qry = "SELECT * FROM tbl_users WHERE station = '" . ($_SESSION['station'] ?? '') . "' AND position = ?";
                                $sh_stmt = $connection->prepare($sh_qry);
                                $sh_stmt->bind_param("s", $position);
                                $position = 'School Head';
                                $sh_stmt->execute();
                                $sh_res = $sh_stmt->get_result();
                                $sh_row = $sh_res->fetch_assoc();
                                $sh_position = ($sh_row && isset($sh_row['position'])) ? $sh_row['position'] : 'School Head';
                                 ?>
            <td><?php echo strtoupper($sh_position); ?></td>
        </tr>
        <tr>
            <td class="label"><strong>STATION/SCHOOL:</strong></td>
            <td>
                <?php
                            $station_parts = explode(' - ', $_SESSION['station'] ?? '');
                            echo strtoupper($station_parts[1] ?? $_SESSION['station'] ?? '');
                            ?>
            </td>
            <td class="label"><strong>DATE OF REVIEW:</strong></td>
            <td><?php echo strtoupper($header_info['current_date'] ?? date('F d, Y')); ?></td>
        </tr>
        <tr>
            <td class="label"><strong>RATING PERIOD:</strong></td>
            <td><?php echo $header_info['rating_period']; ?></td>
            <td colspan="2"></td>
        </tr>
    </table>

    <!-- Main IPCRF Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th rowspan="2" class="kra-header">
                    <strong>KRAs</strong><br>
                    <small>(Key Result Areas)</small>
                </th>
                <th rowspan="2" class="objectives-header">
                    <strong>OBJECTIVES</strong>
                </th>
                <th rowspan="2" class="target-header">
                    <strong>TARGET</strong>
                </th>
                <th rowspan="2" class="development-header">
                    <strong>DEVELOPMENT NEEDS / AREAS FOR ACTION</strong>
                </th>
                <th colspan="2" style="background-color: #f8f9fa;">
                    <strong>CATCH-UP PLAN / PLAN OF ACTIONS & STRATEGIES</strong>
                </th>
            </tr>
            <tr>
                <th class="strategies-header">
                    <strong>STRATEGIES</strong>
                </th>
                <th class="actions-header">
                    <strong>ACTIONS</strong>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php
                include_once 'db/dbconfig.php';
                include_once 'db/ipcrfController.php';
                $ipcrfController = null;
                $user_full_name = '';
                    try {
                        if (isset($connection) && $connection) {
                            $ipcrfController = new IPCRFController($connection);
                            $user_full_name = $ipcrfController->getCurrentUserName();
                        } else {
                            error_log("Database connection not available");
                            $user_full_name = strtoupper($_SESSION['fname'] ?? '') . ' ' . strtoupper($_SESSION['lname'] ?? '');
                        }
                    } catch (Exception $e) {
                        error_log("Error initializing IPCRF Controller: " . $e->getMessage());
                        $user_full_name = strtoupper($_SESSION['fname'] ?? '') . ' ' . strtoupper($_SESSION['lname'] ?? '');
                    }
                ?>
            <?php
            // Fetch IPCRF data from database
            $ipcrf_data = array();
            if ($ipcrfController) {
                try {
                    // Get current user's IPCRF entries
                    $rating_period = 'SY 2025-2026'; // Current rating period
                    $ipcrf_data = $ipcrfController->getIPCRFCatchupData($rating_period);
                } catch (Exception $e) {
                    error_log("Error fetching IPCRF data: " . $e->getMessage());
                    $ipcrf_data = array();
                }
            }

            // Display data if available
            if (!empty($ipcrf_data)) {
                foreach ($ipcrf_data as $index => $row) {
                    ?>
            <tr style="height: auto; min-height: 120px;">
                <!-- KRAs -->
                <td style="border: 2px solid #000; padding: 10px; vertical-align: top; text-align: justify;">
                    <div class="ipcrfdata" style="font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                        <?php echo nl2br(htmlspecialchars($row['kras'] ?? '')); ?>
                    </div>
                    <?php if (isset($row['created_at'])): ?>
                    <?php endif; ?>
                </td>
                <!-- OBJECTIVES -->
                <td style="border: 2px solid #000; padding: 10px; vertical-align: top; text-align: justify;">
                    <div class="ipcrfdata" style="font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                        <?php echo nl2br(htmlspecialchars($row['objectives'] ?? '')); ?>
                    </div>
                </td>
                <!-- TARGET -->
                <td style="border: 2px solid #000; padding: 10px; vertical-align: top; text-align: justify;">
                    <div class="ipcrfdata" style="font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                        <?= nl2br(htmlspecialchars($row['target'] ?? '')) ?>
                    </div>
                </td>
                <!-- DEVELOPMENT NEEDS -->
                <td style="border: 2px solid #000; padding: 10px; vertical-align: top; text-align: justify;">
                    <div class="ipcrfdata" style="font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                        <?= nl2br(htmlspecialchars($row['development_needs'] ?? '')) ?>
                    </div>
                </td>
                <!-- STRATEGIES -->
                <td style="border: 2px solid #000; padding: 10px; vertical-align: top; text-align: justify;">
                    <div class="ipcrfdata" style="font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                        <?= nl2br(htmlspecialchars($row['strategies'] ?? '')) ?>
                    </div>
                </td>
                <!-- ACTIONS -->
                <td style="border: 2px solid #000; padding: 10px; vertical-align: top; text-align: justify;">
                    <div class="ipcrfdata" style="font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                        <?= nl2br(htmlspecialchars($row['actions'] ?? '')) ?>
                    </div>
                </td>
            </tr>
            <?php
                }
            } ?>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="signature-section">
        <table class="signature-table">
            <tr class="sign-label" style="height: auto; width: 100%; min-height: 120px; text-align: justify">
                <!--Query statement for image path picure-->
                <?php       
                $esign_qry = "SELECT `e-sign-path` FROM `tbl_esign` WHERE empnum = ?";
                $esign_stmt = $connection->prepare($esign_qry);

                if ($esign_stmt) {
                    $esign_stmt->bind_param("s", $empnum);
                    $esign_stmt->execute();
                    $result = $esign_stmt->get_result();

                    if ($row = $result->fetch_assoc()) {
                        $esign_path = $row['e-sign-path'];

                        //Convert Windows path to web URL
                        $web_path = "/prime-hrm/school/images/e-sign/" . $empnum . ".png";
                    } else {
                        $web_path = "";
                    }

                    $esign_stmt->close();
                
                }
                ?>
                <td>
                    <div class="signature-label">Ratee:</div>
                </td>
                <td>
                    <div class="signature-label">Rater:</div>
                </td>
                <td>
                    <div class="signature-label">Recommending Approval:</div>
                </td>
                <td>
                    <div class="signature-label">Approving Authority:</div>
                </td>
            </tr>
            <tr class="sign-image" style="height: auto; min-height: 120px; text-align: justify">
                <td>
                    <div class="signature-image"><?php if (!empty($web_path)): ?>
                        <img src="<?= $web_path ?>" alt="E-Signature" style="height: 100px;">
                        <?php else: ?>
                        <p>Not Available</p>
                        <?php endif; ?>
                    </div>
                </td>
                <td>
                    <?php
                        $sh_utype = 1;
                        $sh_qry = "SELECT * FROM tbl_users WHERE station = '" . ($_SESSION['station'] ?? '') . "' AND usertype = ?";
                        $sh_stmt = $connection->prepare($sh_qry);
                        $sh_stmt->bind_param("i", $sh_utype);
                        $sh_stmt->execute();
                        $sh_res =$sh_stmt->get_result();
                        $sh_row =$sh_res->fetch_assoc();
                        if($sh_row){
                                $sh_empnum = $sh_row["empnum"];
                           }
                           else{
                                $sh_empnum = "Not Available";
                           }
                        ?>
                    <div class="signature-image"><?php
                    // Check if user record exists and has empnum
                    if ($sh_row && !empty($sh_row["empnum"])) { 
                        $signature_path = "../images/e-sign/" . htmlspecialchars($sh_row["empnum"]) . ".png";
                        $absolute_path = $_SERVER['DOCUMENT_ROOT'] . "../prime-hrm/school/images/" . htmlspecialchars($sh_row["empnum"]) . ".png";
                        
                        // Verify the image file actually exists
                        if (file_exists($absolute_path)) {
                            echo '<img src="' . $signature_path . '" alt="Signature" style="height: 100px;">';
                        } else {
                            echo '<p class="no-signature">Signature Not Available</p>';
                        }
                    } else {
                        echo '<p class="no-signature">Signature Not Available</p>';
                    }
                     ?>
                    </div>
                </td>
                <td><?php
                           $asds_utype = 14; // if there not in this type it also not available//
                           $asds_qry = "SELECT * FROM tbl_users WHERE `usertype` = ?";
                           $asds_stmt = $connection->prepare($asds_qry);
                           $asds_stmt->bind_param("i", $asds_utype);
                           $asds_stmt->execute();
                           $asds_res =$asds_stmt->get_result();
                           $asds_row =$asds_res->fetch_assoc();
                        ?>
                    <div class="signature-image"> <?php
                    // Check if user record exists and has empnum
                    if ($asds_row && !empty($asds_row["empnum"])) { 
                        $signature_path = "../images/e-sign/" . htmlspecialchars($asds_row["empnum"]) . ".png";
                        $absolute_path = $_SERVER['DOCUMENT_ROOT'] . "../prime-hrm/school/images/" . htmlspecialchars($asds_row["empnum"]) . ".png";
                        
                        // Verify the image file actually exists
                        if (file_exists($absolute_path)) {
                            echo '<img src="' . $signature_path . '" alt="Signature" style="height: 100px;">';
                        } else {
                            echo '<p class="no-signature">Signature Not Available</p>';
                        }
                    } else {
                        echo '<p class="no-signature">Signature Not Available</p>';
                    }
                     ?>
                    </div>
                </td>
                <td> <?php
                           $sds_utype = 6; // if there not in this type it also not available//
                           $sds_qry = "SELECT * FROM tbl_users WHERE `usertype` = ?";
                           $sds_stmt = $connection->prepare($sds_qry);
                           $sds_stmt->bind_param("i", $sds_utype);
                           $sds_stmt->execute();
                           $sds_res =$sds_stmt->get_result();
                           $sds_row =$sds_res->fetch_assoc();
                    ?>
                    <div class="signature-image">
                        <?php
                    // Check if user record exists and has empnum
                    if ($sds_row && !empty($sds_row["empnum"])) { 
                        $signature_path = "../images/e-sign/" . htmlspecialchars($sds_row["empnum"]) . ".png";
                        $absolute_path = $_SERVER['DOCUMENT_ROOT'] . "../prime-hrm/school/images/" . htmlspecialchars($sds_row["empnum"]) . ".png";
                        
                        // Verify the image file actually exists
                        if (file_exists($absolute_path)) {
                            echo '<img src="' . $signature_path . '" alt="Signature" style="height: 100px;">';
                        } else {
                            echo '<p class="no-signature">Signature Not Available</p>';
                        }
                    } else {
                        echo '<p class="no-signature">Signature Not Available</p>';
                    }
                     ?>
                    </div>
                </td>
            </tr>
            <tr class="sign-line" style="height: auto; min-height: 120px; text-align: justify">
                <td>
                    <div class="signature-line"></div>
                </td>
                <td>
                    <div class="signature-line"></div>
                </td>
                <td>
                    <div class="signature-line"></div>
                </td>
                <td>
                    <div class="signature-line"></div>
                </td>
            </tr>
            <tr class="sign-name" style="height: auto; min-height: 120px; text-align: justify">
                <td>
                    <div class="signature-title"><?php echo strtoupper($_SESSION['shortname'] ?? ''); ?></div>
                </td>
                <td>
                    <?php
                        $sh_qry = "SELECT * FROM tbl_users WHERE station = '" . ($_SESSION['station'] ?? '') . "' AND usertype = ?";
                        $sh_stmt = $connection->prepare($sh_qry);
                        $sh_stmt->bind_param("s", $usertype);
                        $usertype = '1';
                        $sh_stmt->execute();
                        $sh_res = $sh_stmt->get_result();
                        $sh_row = $sh_res->fetch_assoc();
                        if ($sh_row && isset($sh_row['fname'])) {
                            $sh_name = $sh_row['fname'].' '.substr((($sh_row['mname']=="") ?"":($sh_row['mname'])), 0, 1).'.'.' '.$sh_row['lname'].''.(($sh_row['extname']=="") ?"":" ".($sh_row['extname']));
                        } else {
                            $sh_name = 'School Head';
                        }
                        ?>
                    <div class="signature-title"><?php echo strtoupper($sh_name);?></div>
                </td>
                <td> <?php
                            $usertype = 14;
                            $asds_qry = "SELECT * FROM tbl_users WHERE `usertype` = ?";
                            $asds_stmt = $connection->prepare($asds_qry);
                            $asds_stmt->bind_param("i", $usertype);
                            $asds_stmt->execute();
                            $asds_res = $asds_stmt->get_result();
                            $asds_row = $asds_res->fetch_assoc();
                            if($asds_row){
                                $asds_name = $asds_row['fname'].' '.substr((($asds_row['mname']=="") ?"":($asds_row['mname'])), 0, 1).'.'.' '.$asds_row['lname'].''.(($asds_row['extname']=="") ?"":" ".($asds_row['extname']));
                                $asds_position = $asds_row["position"];
                            }
                            else{
                                $asds_name = "NILITA L. RAGAY";
                                $asds_position = "OIC - ASDS";
                            }
                     ?>
                    <div class="signature-title"><?php echo strtoupper($asds_name);?></div>
                </td>
                <td><?php
                            $usertype = 6;
                            $sds_qry = "SELECT * FROM tbl_users WHERE `usertype` = ?";
                            $sds_stmt = $connection->prepare($sds_qry);
                            $sds_stmt->bind_param("i", $usertype);
                            $sds_stmt->execute();
                            $sds_res = $sds_stmt->get_result();
                            $sds_row = $sds_res->fetch_assoc();
                            $sds_name = $sds_row['fname'].' '.substr((($sds_row['mname']=="") ?"":($sds_row['mname'])), 0, 1).'.'.' '.$sds_row['lname'].''.(($sds_row['extname']=="") ?"":" ".($sds_row['extname']));
                            ?>
                    <div class="signature-title"><?php echo strtoupper($sds_name); ?></div>
                </td>
            </tr>
            <tr class="sign-position" style="height: auto; min-height: 120px; text-align: justify">
                <td>
                    <div class="signature-position"><?php echo strtoupper($_SESSION['position'] ?? ''); ?></div>
                </td>
                <td><?php
                            $sh_qry = "SELECT * FROM tbl_users WHERE station = '" . ($_SESSION['station'] ?? '') . "' AND position = ?";
                            $sh_stmt = $connection->prepare($sh_qry);
                            $sh_stmt->bind_param("s", $position);
                            $position = 'School Head';
                            $sh_stmt->execute();
                            $sh_res = $sh_stmt->get_result();
                            $sh_row = $sh_res->fetch_assoc();
                            $sh_position = ($sh_row && isset($sh_row['position'])) ? $sh_row['position'] : 'School Head';
                                ?>
                    <div class="signature-position"> <?php echo strtoupper($sh_position); ?></div>
                </td>
                <td>
                    <div class="signature-position">
                        <?= strtoupper($asds_position)== "" ? "OIC - ASDS": $asds_position;?></div>
                </td>
                <td>
                    <div class="signature-position">
                        <?= strtoupper($sds_row['position']) =="" ? "SCHOOLS DIVISION SUPERINTENDENT":$sds_row['position']; ?>
                    </div>
                </td>
            </tr>
        </table>
    </div>

    <script>
    function applyPrintSettings() {
        const size = document.getElementById("paperSize").value;
        const orientation = document.getElementById("orientation").value;

        // Remove existing @page style if any
        const existing = document.getElementById("dynamicPrintStyle");
        if (existing) existing.remove();

        // Create a new style tag
        const style = document.createElement("style");
        style.id = "dynamicPrintStyle";
        style.innerHTML = `@page { size: ${size} ${orientation}; margin: 0.5in; }`;
        document.head.appendChild(style);

        alert(`Applied: ${size} ${orientation}. Now press Print.`);
    }
    // Auto-print when page loads (optional)
    // window.onload = function() { window.print(); };
    </script>


</body>

</html>